"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_controller_new_1 = require("../controllers/auth.controller.new");
const auth_new_1 = require("../middleware/auth.new");
const router = express_1.default.Router();
/**
 * @route POST /api/auth/admin/login
 * @desc Admin login
 * @access Public
 */
router.post('/admin/login', auth_controller_new_1.loginLimiter, [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please enter a valid email address'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 1 })
        .withMessage('Password is required'),
], auth_controller_new_1.adminLogin);
/**
 * @route POST /api/auth/admin/logout
 * @desc Admin logout
 * @access Public
 */
router.post('/admin/logout', auth_controller_new_1.adminLogout);
/**
 * @route GET /api/auth/admin/profile
 * @desc Get current admin profile
 * @access Private (Admin only)
 */
router.get('/admin/profile', auth_new_1.requireAdmin, auth_controller_new_1.getCurrentAdmin);
/**
 * @route POST /api/auth/admin/change-password
 * @desc Change admin password
 * @access Private (Admin only)
 */
router.post('/admin/change-password', auth_new_1.requireAdmin, [
    (0, express_validator_1.body)('currentPassword')
        .notEmpty()
        .withMessage('Current password is required'),
    (0, express_validator_1.body)('newPassword')
        .isLength({ min: 12 })
        .withMessage('New password must be at least 12 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
        .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
], auth_controller_new_1.changePassword);
/**
 * @route GET /api/auth/debug
 * @desc Debug authentication state
 * @access Public
 */
router.get('/debug', (req, res) => {
    const token = req.cookies.auth_token;
    res.json({
        success: true,
        debug: {
            hasCookie: !!token,
            cookieValue: token ? 'present' : 'missing',
            cookies: Object.keys(req.cookies),
            headers: {
                authorization: req.headers.authorization,
                cookie: req.headers.cookie
            }
        }
    });
});
/**
 * @route POST /api/auth/clear-cookies
 * @desc Clear all authentication cookies (for debugging)
 * @access Public
 */
router.post('/clear-cookies', (req, res) => {
    // Clear all possible authentication cookies
    res.clearCookie('auth_token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/'
    });
    // Clear any other potential cookies
    res.clearCookie('token');
    res.clearCookie('admin_token');
    res.clearCookie('session');
    res.json({
        success: true,
        message: 'All authentication cookies cleared'
    });
});
/**
 * @route POST /api/auth/reset-admin-account
 * @desc Reset admin account (clear failed attempts and unlock)
 * @access Public (for debugging only)
 */
router.post('/reset-admin-account', async (req, res) => {
    try {
        const { email } = req.body;
        if (!email) {
            return res.status(400).json({
                success: false,
                message: 'Email is required'
            });
        }
        // Import Admin model
        const { Admin } = await Promise.resolve().then(() => __importStar(require('../models/Admin')));
        // Find admin by email first
        const admin = await Admin.findByEmail(email);
        if (!admin) {
            return res.status(404).json({
                success: false,
                message: 'Admin not found'
            });
        }
        // Reset failed login attempts and unlock account
        await Admin.updateFailedLoginAttempts(admin.id, 0, undefined // Clear lock_until
        );
        res.json({
            success: true,
            message: 'Admin account reset successfully',
            data: {
                email,
                id: admin.id,
                previousAttempts: admin.failedLoginAttempts,
                wasLocked: !!admin.lockUntil,
                reset: true
            }
        });
    }
    catch (error) {
        console.error('Reset admin account error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to reset admin account',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
/**
 * @route GET /api/auth/verify
 * @desc Verify authentication status
 * @access Private
 */
router.get('/verify', auth_new_1.authenticate, auth_controller_new_1.verifyAuth);
/**
 * @route GET /api/auth/admin/verify
 * @desc Verify admin authentication status
 * @access Private (Admin only)
 */
router.get('/admin/verify', auth_new_1.requireAdmin, auth_controller_new_1.verifyAuth);
exports.default = router;
