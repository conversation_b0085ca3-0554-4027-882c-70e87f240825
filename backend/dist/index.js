"use strict";
/**
 * DATABASE MIGRATION NOTICE
 *
 * This application has been fully migrated from Sequelize to PostgreSQL.
 * All database operations now use direct PostgreSQL queries for improved performance.
 *
 * The migration was completed and Prisma has been completely removed.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
// Load environment variables first
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const morgan_1 = __importDefault(require("morgan"));
const path_1 = __importDefault(require("path"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const compression_1 = __importDefault(require("compression"));
const helmet_1 = __importDefault(require("helmet"));
const express_slow_down_1 = __importDefault(require("express-slow-down"));
const database_1 = require("./config/database");
const Admin_1 = require("./models/Admin");
const envValidator_1 = __importDefault(require("./utils/envValidator"));
const apiCache_middleware_1 = __importDefault(require("./middleware/apiCache.middleware"));
const validation_middleware_1 = __importDefault(require("./middleware/validation.middleware"));
const rateLimiting_middleware_1 = __importDefault(require("./middleware/rateLimiting.middleware"));
const advancedCSP_1 = require("./middleware/advancedCSP");
const error_middleware_1 = require("./middleware/error.middleware");
// Import routes
const auth_new_1 = __importDefault(require("./routes/auth.new")); // Secure authentication system with HTTP-only cookies
const scholarship_routes_1 = __importDefault(require("./routes/scholarship.routes"));
const user_routes_1 = __importDefault(require("./routes/user.routes"));
const admin_password_routes_1 = __importDefault(require("./routes/admin.password.routes"));
const messages_1 = __importDefault(require("./routes/messages"));
const newsletter_1 = __importDefault(require("./routes/newsletter"));
const security_dashboard_routes_1 = __importDefault(require("./routes/security.dashboard.routes"));
const twoFactor_routes_1 = __importDefault(require("./routes/twoFactor.routes"));
const deviceTrust_routes_1 = __importDefault(require("./routes/deviceTrust.routes"));
const security_csp_routes_1 = __importDefault(require("./routes/security.csp.routes"));
// Validate and load environment variables
if (!(0, envValidator_1.default)()) {
    console.error('Environment validation failed. Exiting application.');
    process.exit(1);
}
// CRITICAL: Validate JWT secret strength for industry standards
const jwtSecret = process.env.JWT_SECRET;
if (!jwtSecret || jwtSecret.length < 32 || jwtSecret === 'your-secret-key' || jwtSecret === 'fallback-secret-key') {
    console.error('❌ SECURITY ERROR: JWT_SECRET must be at least 32 characters and not use default values');
    console.error('   Current JWT_SECRET length:', (jwtSecret === null || jwtSecret === void 0 ? void 0 : jwtSecret.length) || 0);
    console.error('   Please set a strong JWT_SECRET in your environment variables');
    process.exit(1);
}
console.log('✅ JWT secret validation passed');
// Log application startup information
console.info(`Starting MaBourse backend in ${process.env.NODE_ENV} mode`);
console.info(`Server port: ${process.env.PORT}`);
console.info(`Database: ${(_a = process.env.DATABASE_URL) === null || _a === void 0 ? void 0 : _a.replace(/:[^:]*@/, ':****@')}`);
// Initialize PostgreSQL database connection
const db = (0, database_1.initializeDatabase)();
// Create Express app
const app = (0, express_1.default)();
// Middleware
// Configure CORS based on environment
const corsOrigins = process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000', 'http://localhost:3001'];
app.use((0, cors_1.default)({
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin)
            return callback(null, true);
        // Allow file:// protocol for testing
        if (origin.startsWith('file://'))
            return callback(null, true);
        // Allow configured origins
        if (corsOrigins.includes(origin))
            return callback(null, true);
        // In development, be more permissive
        if (process.env.NODE_ENV === 'development') {
            return callback(null, true);
        }
        return callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Debug-Mode'],
    exposedHeaders: ['Set-Cookie']
}));
// Log CORS configuration
console.info(`CORS configured for origins: ${corsOrigins.join(', ')}`);
// Basic middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use((0, cookie_parser_1.default)()); // Add cookie parser middleware
app.use((0, morgan_1.default)('dev'));
// PROFESSIONAL-GRADE SECURITY MIDDLEWARE
app.use((0, helmet_1.default)({
    // Content Security Policy
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    // HTTP Strict Transport Security
    hsts: {
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true
    },
    // X-Frame-Options
    frameguard: { action: 'deny' },
    // X-Content-Type-Options
    noSniff: true,
    // Referrer Policy
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
    // Cross-Origin-Embedder-Policy
    crossOriginEmbedderPolicy: false, // Disable for API
    // Cross-Origin-Resource-Policy
    crossOriginResourcePolicy: { policy: 'same-origin' },
    // Cross-Origin-Opener-Policy
    crossOriginOpenerPolicy: { policy: 'same-origin' },
    // Origin-Agent-Cluster
    originAgentCluster: true,
    // X-DNS-Prefetch-Control
    dnsPrefetchControl: { allow: false },
    // X-Download-Options
    ieNoOpen: true,
    // X-Permitted-Cross-Domain-Policies
    permittedCrossDomainPolicies: false,
    // X-XSS-Protection
    xssFilter: true
}));
app.use((0, compression_1.default)()); // Compress responses
// PROFESSIONAL SLOW DOWN MIDDLEWARE - Progressive delays for suspicious activity
const speedLimiter = (0, express_slow_down_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    delayAfter: 10, // Allow 10 requests per window without delay
    delayMs: (hits) => hits * 100, // Add 100ms delay per request after delayAfter
    maxDelayMs: 5000, // Maximum delay of 5 seconds
    skipSuccessfulRequests: true, // Don't count successful requests
    skipFailedRequests: false, // Count failed requests
    keyGenerator: (req) => req.ip || 'unknown'
});
app.use('/api/auth', speedLimiter); // Apply to authentication routes
app.use(validation_middleware_1.default.securityHeaders); // Add additional security headers
app.use(validation_middleware_1.default.sanitizeUrlParams); // Sanitize URL parameters
// Advanced Content Security Policy
const environment = process.env.NODE_ENV === 'production' ? 'production' : 'development';
app.use((0, advancedCSP_1.createCSP)(environment)); // Apply CSP based on environment
// Caching middleware for public endpoints
app.use(apiCache_middleware_1.default.addCacheHeaders()); // Add cache headers to responses
// Serve static files from uploads directory
app.use('/uploads', express_1.default.static(path_1.default.join(__dirname, '../uploads')));
// Apply general API rate limiting to all routes
app.use('/api', rateLimiting_middleware_1.default.apiLimiter);
// Routes
// PROFESSIONAL SECURE AUTHENTICATION SYSTEM (HTTP-only cookies + Enhanced Security)
app.use('/api/auth', rateLimiting_middleware_1.default.authLimiter, // Rate limiting
validation_middleware_1.default.sanitizeRequestBody, // Input sanitization
auth_new_1.default // Secure auth routes
);
// Shared routes with caching for public endpoints
app.use('/api/scholarships', apiCache_middleware_1.default.cacheApiResponse(300), validation_middleware_1.default.sanitizeRequestBody, scholarship_routes_1.default);
app.use('/api/users', validation_middleware_1.default.sanitizeRequestBody, user_routes_1.default);
app.use('/api/messages', rateLimiting_middleware_1.default.contactFormLimiter, validation_middleware_1.default.sanitizeRequestBody, messages_1.default);
app.use('/api/newsletter', rateLimiting_middleware_1.default.contactFormLimiter, validation_middleware_1.default.sanitizeRequestBody, newsletter_1.default);
// Admin password routes must be registered before admin routes to avoid authentication middleware
app.use('/api/admin/password', rateLimiting_middleware_1.default.passwordResetLimiter, validation_middleware_1.default.sanitizeRequestBody, admin_password_routes_1.default); // Re-enabled for admin functionality
// Security dashboard routes (admin only)
app.use('/api/security', rateLimiting_middleware_1.default.authLimiter, validation_middleware_1.default.sanitizeRequestBody, security_dashboard_routes_1.default);
// Two-factor authentication routes
app.use('/api/2fa', rateLimiting_middleware_1.default.authLimiter, validation_middleware_1.default.sanitizeRequestBody, twoFactor_routes_1.default);
// Device trust management routes
app.use('/api/devices', rateLimiting_middleware_1.default.authLimiter, validation_middleware_1.default.sanitizeRequestBody, deviceTrust_routes_1.default);
// CSP security routes
app.use('/api/security', rateLimiting_middleware_1.default.authLimiter, validation_middleware_1.default.sanitizeRequestBody, security_csp_routes_1.default);
// Health check endpoint with caching
app.get('/api/health', apiCache_middleware_1.default.cacheApiResponse(60), (req, res) => {
    res.json({
        status: 'ok',
        message: 'Server is running',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0'
    });
});
// Test database connection endpoint
app.get('/api/test-db', async (req, res) => {
    try {
        // Test database connection
        const isConnected = await (0, database_1.testConnection)();
        if (!isConnected) {
            throw new Error('Database connection test failed');
        }
        // Test model queries
        const adminCount = await Admin_1.Admin.count();
        const { User } = await Promise.resolve().then(() => __importStar(require('./models/User')));
        const userCount = await User.count();
        const { Scholarship } = await Promise.resolve().then(() => __importStar(require('./models/Scholarship')));
        const scholarshipCount = await Scholarship.count();
        res.json({
            success: true,
            message: 'Database connection is working properly',
            data: {
                adminCount,
                userCount,
                scholarshipCount
            }
        });
    }
    catch (error) {
        console.error('Database test error:', error);
        res.status(500).json({
            success: false,
            message: 'Database connection test failed',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
// Cache statistics endpoint (admin only)
app.get('/api/admin/cache-stats', (req, res) => {
    if (!req.user || !req.user.isMainAdmin) {
        return res.status(403).json({
            success: false,
            message: 'Access denied',
            error: 'Admin privileges required'
        });
    }
    res.json({
        success: true,
        message: 'Cache statistics retrieved successfully',
        data: {
            apiCache: apiCache_middleware_1.default.getCacheStats()
            // Additional cache stats can be added here
        }
    });
});
// Professional error handling middleware
app.use(error_middleware_1.notFoundHandler); // Handle 404 errors
app.use(error_middleware_1.errorHandler); // Handle all other errors
// Import cleanup utilities
// import { performDataCleanup } from './utils/cleanupUtils'; // Temporarily disabled
// Initialize database and start server
const PORT = process.env.PORT || 5000;
// Start server
const startServer = async () => {
    try {
        // Test database connection
        const isConnected = await (0, database_1.testConnection)();
        if (!isConnected) {
            throw new Error('Database connection failed');
        }
        console.log('PostgreSQL database connection established successfully.');
        // Check if main admin exists (temporarily disabled for debugging)
        // const mainAdmin = await Admin.findMainAdmin();
        // Only create admin if none exists (this should already be handled by migration)
        // if (!mainAdmin) {
        //   console.log('Creating main admin...');
        //   await Admin.create({
        //     name: 'Main Admin',
        //     email: '<EMAIL>',
        //     password: 'admin123',
        //     role: 'super_admin',
        //     privileges: ['all'],
        //     isMainAdmin: true,
        //     failedLoginAttempts: 0,
        //     twoFactorEnabled: false
        //   });
        //   console.log('Main admin created successfully.');
        // }
        // Run data cleanup to fix any duplicate data issues
        // Temporarily disabled to fix server startup issues
        // console.log('Running data cleanup process...');
        // await performDataCleanup();
        // Start server
        console.log('About to start server on port', PORT);
        const server = app.listen(PORT, () => {
            console.log(`✅ Server is running on port ${PORT}`);
            console.log(`🌐 Health check: http://localhost:${PORT}/health`);
            console.log(`📚 API docs: http://localhost:${PORT}/api-docs`);
        });
        server.on('error', (error) => {
            console.error('Server error:', error);
        });
    }
    catch (error) {
        console.error('Failed to start server:', error);
        await (0, database_1.closeDatabase)();
        process.exit(1);
    }
};
// Handle application shutdown
process.on('SIGINT', async () => {
    await (0, database_1.closeDatabase)();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    await (0, database_1.closeDatabase)();
    process.exit(0);
});
startServer();
